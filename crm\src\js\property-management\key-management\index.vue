<template>
    <div class="key-management-container">
        <!-- Title Bar -->
        <div class="title-bar">
            <h3 class="title">{{ $t('page.keys_list') }}</h3>
            <div class="title-actions">
                <Button
                    class="add-btn border"
                    size="small"
                    @click="handleAddKey"
                >
                    <i class="icon2017 icon-add_bold"></i>
                </Button>
            </div>
        </div>

        <!-- Cards List -->
        <div class="cards-container">
            <div
                v-for="key in keysList"
                :key="key.id"
                class="key-card"
            >
                <!-- Header Row -->
                <div class="card-row header-row">
                    <span class="key-code">{{ key.keyCode }}</span>
                    <div class="status-wrapper">
                        <span
                            class="status-dot"
                            :class="getStatusClass(key.status)"
                        ></span>
                        <span class="status-text">{{ getStatusText(key.status) }}</span>
                    </div>
                </div>

                <!-- Purpose Row -->
                <div class="card-row purpose-row">
                    <span class="purpose-text">{{ key.purpose }}</span>
                </div>

                <!-- Holder Info Row -->
                <div class="card-row holder-row" v-if="key.holder">
                    <div class="holder-info">
                        <div class="info-item">
                            <i class="icon2017 icon-people_01"></i>
                            <span>{{ key.holder.name }}</span>
                        </div>
                        <div class="info-item">
                            <i class="icon2017" :class="key.holder.email ? 'icon-mail' : 'icon-call_01'"></i>
                            <span>{{ key.holder.email || key.holder.phone }}</span>
                        </div>
                    </div>
                </div>

                <!-- Special Message Row (for lost/archived keys) -->
                <div class="card-row message-row" v-if="key.message">
                    <p class="message-text">{{ key.message }}</p>
                </div>

                <!-- Statistics Row -->
                <div class="card-row stats-row">
                    <span class="stat-item">{{ $t('key_details.total_key') }} {{ key.totalKeys }}</span>
                    <span class="divider"></span>
                    <span class="stat-item">{{ $t('key_details.last_activity') }} {{ key.lastActivity }}</span>
                </div>

                <!-- Actions Row -->
                <div class="card-row actions-row">
                    <Button
                        v-for="action in getActionsForKey(key)"
                        :key="action.label"
                        class="action-btn border"
                        size="small"
                        @click="handleAction(action.type, key)"
                    >
                        {{ action.label }}
                        <i v-if="action.hasDropdown" class="icon2017 icon-arrow_01_down"></i>
                    </Button>
                </div>
            </div>
        </div>

        <!-- Key Dialog -->
        <KeyDialog
            :isShow="dialog.isShow"
            :mode="dialog.mode"
            :keyData="dialog.keyData"
            @close="handleDialogClose"
            @confirm="handleDialogConfirm"
        />
    </div>
</template>

<script>
import { components } from "common";
import KeyDialog from "./detail/components/KeyDialog.vue";

const { Button } = components;

export default {
    name: "KeyManagement",
    components: {
        Button,
        KeyDialog
    },
    data() {
        return {
            keysList: [
                {
                    id: 1,
                    keyCode: "MK-001-FRONT",
                    status: "available",
                    purpose: this.$t('key_details.office_safe'),
                    description: this.$t('key_details.office_safe'),
                    holder: {
                        name: "Ralph Edwards",
                        email: "<EMAIL>"
                    },
                    holderType: "system",
                    holderId: 1,
                    holderName: "Ralph Edwards",
                    storageLocation: "Main Office",
                    totalKeys: 3,
                    lastActivity: this.$t('key_details.time_2_hours_ago')
                },
                {
                    id: 2,
                    keyCode: "TK-001-FRONT",
                    status: "checked_out",
                    purpose: this.$t('key_details.for_viewing_appointment'),
                    description: this.$t('key_details.for_viewing_appointment'),
                    holder: {
                        name: "Eleanor Pena",
                        phone: "+44 20 1234 0931"
                    },
                    holderType: "system",
                    holderId: 2,
                    holderName: "Eleanor Pena",
                    storageLocation: "Reception",
                    totalKeys: 3,
                    lastActivity: this.$t('key_details.time_1_week_ago')
                },
                {
                    id: 3,
                    keyCode: "CK-001-TEMP",
                    status: "lost",
                    purpose: this.$t('key_details.the_keys_are_lost'),
                    description: this.$t('key_details.the_keys_are_lost'),
                    message: this.$t('key_details.lost_message'),
                    totalKeys: 2,
                    lastActivity: "just now"
                },
                {
                    id: 4,
                    keyCode: "TK-001-FRONT",
                    status: "archived",
                    purpose: this.$t('key_details.the_keys_are_archived'),
                    description: this.$t('key_details.the_keys_are_archived'),
                    message: this.$t('key_details.archived_message'),
                    totalKeys: 3,
                    lastActivity: this.$t('key_details.time_1_week_ago')
                }
            ],
            dialog: {
                isShow: false,
                mode: "create",
                keyData: null
            }
        };
    },
    methods: {
        getStatusClass(status) {
            const statusMap = {
                available: "status-available",
                checked_out: "status-checked-out",
                lost: "status-lost",
                archived: "status-archived"
            };
            return statusMap[status] || "";
        },
        getStatusText(status) {
            const textMap = {
                available: this.$t('filters.status.available'),
                checked_out: this.$t('filters.status.checked_out'),
                lost: this.$t('filters.status.lost'),
                archived: this.$t('filters.status.archived')
            };
            return textMap[status] || status;
        },
        getActionsForKey(key) {
            const baseActions = [
                { label: this.$t('actions.key_log'), type: "keyLog" }
            ];

            switch (key.status) {
                case "available":
                    return [
                        { label: this.$t('actions.check_out'), type: "checkout" },
                        ...baseActions,
                        { label: this.$t('actions.edit'), type: "edit" },
                        { label: this.$t('actions.archive'), type: "archive" }
                    ];
                case "checked_out":
                    return [
                        { label: this.$t('actions.check_in'), type: "checkin" },
                        { label: this.$t('actions.report_lost'), type: "reportLost" },
                        ...baseActions,
                        { label: this.$t('actions.more'), type: "more", hasDropdown: true }
                    ];
                case "lost":
                case "archived":
                    return [
                        ...baseActions,
                        { label: this.$t('actions.edit'), type: "edit" },
                        { label: this.$t('actions.archive'), type: "archive" }
                    ];
                default:
                    return baseActions;
            }
        },
        handleAddKey() {
            this.dialog = {
                isShow: true,
                mode: "create",
                keyData: null
            };
        },
        handleAction(actionType, key) {
            console.log(`Action: ${actionType} for key:`, key);

            if (actionType === "edit") {
                this.openEditDialog(key);
            }
        },
        openEditDialog(key) {
            const keyData = {
                id: key.id,
                photo: key.photo || "",
                keyCode: key.keyCode,
                description: key.description || key.purpose,
                status: key.status,
                storageLocation: key.storageLocation || "",
                holderType: key.holderType || "",
                holderId: key.holderId || "",
                holderName: key.holderName || (key.holder ? key.holder.name : ""),
                totalKeys: key.totalKeys
            };

            this.dialog = {
                isShow: true,
                mode: "edit",
                keyData
            };
        },
        handleDialogClose() {
            this.dialog.isShow = false;
        },
        handleDialogConfirm(formData) {
            console.log("Dialog confirmed with data:", formData);

            if (this.dialog.mode === "create") {
                // 创建新钥匙
                const newKey = {
                    id: this.keysList.length + 1,
                    keyCode: formData.keyCode,
                    status: formData.status,
                    purpose: formData.description,
                    description: formData.description,
                    storageLocation: formData.storageLocation,
                    holderType: formData.holderType,
                    holderId: formData.holderId,
                    holderName: formData.holderName,
                    totalKeys: formData.totalKeys,
                    lastActivity: this.$t('key_details.time_just_now'),
                    holder: null
                };

                // 如果有持有人，添加持有人信息
                if (formData.holderType && formData.holderName) {
                    const holderInfo = {
                        name: formData.holderName
                    };

                    // 添加联系方式（在实际应用中应该从系统获取）
                    if (formData.holderType === "system") {
                        holderInfo.email = this.$t('key_details.contact_email');
                    } else {
                        holderInfo.phone = this.$t('key_details.contact_phone');
                    }

                    newKey.holder = holderInfo;
                }

                this.keysList.unshift(newKey);
            } else if (this.dialog.mode === "edit") {
                // 编辑现有钥匙
                const keyIndex = this.keysList.findIndex(k => k.id === this.dialog.keyData.id);
                if (keyIndex !== -1) {
                    const updatedKey = {
                        ...this.keysList[keyIndex],
                        keyCode: formData.keyCode,
                        status: formData.status,
                        purpose: formData.description,
                        description: formData.description,
                        storageLocation: formData.storageLocation,
                        holderType: formData.holderType,
                        holderId: formData.holderId,
                        holderName: formData.holderName,
                        totalKeys: formData.totalKeys,
                        lastActivity: "just now"
                    };

                    // 更新持有人信息
                    if (formData.holderType && formData.holderName) {
                        const holderInfo = {
                            name: formData.holderName
                        };

                        // 添加联系方式（在实际应用中应该从系统获取）
                        if (formData.holderType === "system") {
                            holderInfo.email = this.$t('key_details.contact_email');
                        } else {
                            holderInfo.phone = this.$t('key_details.contact_phone');
                        }

                        updatedKey.holder = holderInfo;
                    } else {
                        updatedKey.holder = null;
                    }

                    this.$set(this.keysList, keyIndex, updatedKey);
                }
            }

            this.dialog.isShow = false;
        }
    }
};
</script>

<style lang="less" scoped>
.key-management-container {
    background: #f6f7fb;
    min-height: 100vh;
    padding: 20px;
}

.title-bar {
    background: #ffffff;
    border: 1px solid #ebecf1;
    border-radius: 6px 6px 0 0;
    padding: 0 15px 0 20px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0;

    .title {
        font-family: SF Pro;
        font-weight: 510;
        font-size: 16px;
        line-height: 1.5em;
        color: #202437;
        margin: 0;
    }

    .title-actions {
        display: flex;
        align-items: center;
        gap: 10px;

        .add-btn {
            width: 24px;
            height: 24px;
            min-width: 24px;
            padding: 0;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;

            i {
                font-size: 14px;
                color: #797e8b;
            }
        }
    }
}

.cards-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.key-card {
    background: #ffffff;
    border: 1px solid #ebecf1;
    border-radius: 12px;
    padding: 0 20px;
    width: 480px;
    box-sizing: border-box;

    .card-row {
        display: flex;
        align-items: center;

        &.header-row {
            justify-content: space-between;
            padding: 10px 0;

            .key-code {
                font-family: SF Pro Text;
                font-weight: 400;
                font-size: 12px;
                line-height: 1.67em;
                color: #797e8b;
                flex: 1;
            }

            .status-wrapper {
                display: flex;
                align-items: center;
                gap: 8px;

                .status-dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    opacity: 0.8;

                    &.status-available {
                        background: #20c472;
                    }
                    &.status-checked-out {
                        background: #5d51e2;
                    }
                    &.status-lost {
                        background: #f0454c;
                    }
                    &.status-archived {
                        background: #c6c8d1;
                    }
                }

                .status-text {
                    font-family: SF Pro Text;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 1.67em;
                    color: #515666;
                }
            }
        }

        &.purpose-row {
            justify-content: center;
            align-self: stretch;

            .purpose-text {
                font-family: SF Pro Text;
                font-weight: 500;
                font-size: 14px;
                line-height: 1.43em;
                color: #202437;
                width: 440px;
                text-align: left;
            }
        }

        &.holder-row {
            flex-direction: column;
            align-self: stretch;
            gap: 5px;
            padding: 10px 0;

            .holder-info {
                display: flex;
                flex-direction: column;
                align-self: stretch;
                gap: 5px;

                .info-item {
                    display: flex;
                    align-items: center;
                    align-self: stretch;
                    gap: 8px;

                    i {
                        width: 12px;
                        height: 12px;
                        color: #515666;
                        font-size: 12px;
                    }

                    span {
                        font-family: SF Pro;
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 1.67em;
                        color: #515666;
                    }
                }
            }
        }

        &.message-row {
            flex-direction: column;
            align-self: stretch;
            gap: 5px;
            padding: 10px 0;
            height: 65px;

            .message-text {
                font-family: SF Pro;
                font-weight: 400;
                font-size: 12px;
                line-height: 1.67em;
                color: #515666;
                margin: 0;
                flex: 1;
            }
        }

        &.stats-row {
            align-items: center;
            align-self: stretch;
            gap: 10px;
            padding: 0 0 10px;
            border-bottom: 1px solid #ebecf1;

            .stat-item {
                font-family: SF Pro;
                font-weight: 400;
                font-size: 12px;
                line-height: 1.67em;
                color: #a0a3af;
            }

            .divider {
                width: 0;
                height: 12px;
                border-left: 1px solid #ebecf1;
            }
        }

        &.actions-row {
            justify-content: stretch;
            align-items: stretch;
            align-self: stretch;
            gap: 10px;
            padding: 10px 0;

            .action-btn {
                flex: 1;
                height: auto;
                min-width: auto;
                padding: 5px 15px;
                border-radius: 30px;
                border: 1px solid #e1e2e6;
                background: #ffffff;

                font-family: SF Pro;
                font-weight: 400;
                font-size: 14px;
                line-height: 1.43em;
                text-align: center;
                color: #515666;

                display: flex;
                justify-content: center;
                align-items: center;
                gap: 10px;

                i {
                    font-size: 12px;
                    color: #a0a3af;
                }

                &:hover {
                    box-shadow: 0px 1px 3px rgba(0, 10, 30, 0.1);
                }
            }
        }
    }
}
</style>
